<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.copypolicy.ClmGlobalReturnInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.copypolicy.ClmGlobalReturnInfo">
        <id column="id" property="id" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="rc00_rciv_year" property="rc00RcivYear" />
        <result column="rc00_rciv_seq_num" property="rc00RcivSeqNum" />
        <result column="ac00_rciv_date" property="ac00RcivDate" />
        <result column="ac00_rciv_seq_num" property="ac00RcivSeqNum" />
        <result column="agrm_seq_num" property="agrmSeqNum" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="sub_times" property="subTimes" />
    </resultMap>
    <update id="updateByReportNo">
        update clm_global_return_info cgri set agrm_seq_num = #{agrmSeqNum} ,ac00_rciv_date = #{ac00RcivDate} ,ac00_rciv_seq_num = #{ac00RcivSeqNum},sub_times = #{subTimes} where report_no =#{reportNo};
    </update>
    <select id="selectByReportNo" resultType="com.paic.ncbs.claim.dao.entity.copypolicy.ClmGlobalReturnInfo">
        select * from clm_global_return_info where report_no = #{reportNo}
    </select>

</mapper>
