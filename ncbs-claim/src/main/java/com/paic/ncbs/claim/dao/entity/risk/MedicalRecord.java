package com.paic.ncbs.claim.dao.entity.risk;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 就医轨迹表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
@Getter
@Setter
@TableName("clms_medical_record")
public class MedicalRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 报案号(案件号)
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 事故人员名称
     */
    @TableField("accident_name")
    private String accidentName;

    /**
     * 事故人员证件号码
     */
    @TableField("certi_no")
    private String certiNo;

    /**
     * 数据来源(Q01:亿保创元)
     */
    @TableField("data_source")
    private String dataSource;

    /**
     * 风控评分
     */
    @TableField("risk_score")
    private String riskScore;

    /**
     * 风险标签
     */
    @TableField("risk_label")
    private String riskLabel;

    /**
     * 审批人
     */
    @TableField("audit_by")
    private String auditBy;

    /**
     * 审批时间
     */
    @TableField("audit_time")
    private Date auditTime;

    /**
     * 审批说明
     */
    @TableField("audit_remark")
    private String auditRemark;

    /**
     * 状态(0：待审核 1：审核通过 2：已回盘 3：审核不通过)
     */
    @TableField("status")
    private String status;

    /**
     * 回盘时间
     */
    @TableField("back_time")
    private Date backTime;

    /**
     * 调查定性
     */
    @TableField("survey_result")
    private String surveyResult;

    /**
     * 调查结论
     */
    @TableField("survey_desc")
    private String surveyDesc;

    /**
     * 回销人
     */
    @TableField("deal_by")
    private String dealBy;

    /**
     * 回销时间
     */
    @TableField("deal_time")
    private Date dealTime;

    /**
     * 是否回销，1-是 0：否
     */
    @TableField("deal_flag")
    private String dealFlag;

    /**
     * 是否有效，1-是 0：否
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人员
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;
}
