package com.paic.ncbs.claim.dao.entity.risk;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 中保信理赔综合风险查询表
 */
@Data
@TableName("clm_risk_cbit_composite_apply")
public class ClmRiskCbitCompositeApply {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建人员
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 申请人员
     */
    @TableField("apply_user")
    private String applyUser;

    /**
     * 出险人姓名
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 出险人证件号码
     */
    @TableField("certificate_no")
    private String certificateNo;

    /**
     * 出险日期
     */
    @TableField("accident_date")
    private Date accidentDate;

    /**
     * 01-待审核，02-审核不通过，03-待回盘，04-已回盘
     */
    @TableField("apply_status")
    private String applyStatus;

    /**
     * 结果返回时间
     */
    @TableField("result_time")
    private Date resultTime;

    /**
     * 0-未回销，1-已回销
     */
    @TableField("write_back")
    private String writeBack;

    /**
     * 回销说明
     */
    @TableField("write_back_explain")
    private String writeBackExplain;

    /**
     * 创建时间
     */
    @TableField(value = "sys_ctime", fill = FieldFill.INSERT)
    private Date sysCtime;

    /**
     * 修改时间
     */
    @TableField(value = "sys_utime", fill = FieldFill.INSERT_UPDATE)
    private Date sysUtime;

    /**
     * 调查定性，01-正常件、02-参考件、03-异常件、04-其他
     */
    @TableField("write_back_decide")
    private String writeBackDecide;

    /**
     * 回销人
     */
    @TableField("write_back_user")
    private String writeBackUser;

    /**
     * 回销日期
     */
    @TableField("write_back_date")
    private Date writeBackDate;

}
