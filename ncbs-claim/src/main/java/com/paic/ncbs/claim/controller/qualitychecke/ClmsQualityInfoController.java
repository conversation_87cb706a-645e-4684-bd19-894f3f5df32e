package com.paic.ncbs.claim.controller.qualitychecke;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelCommonException;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.qualitychecke.*;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.qualitychecke.ClmsQualityInfoMapper;
import com.paic.ncbs.claim.dao.mapper.qualitychecke.ImportQualityRecordMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentUserMapper;
import com.paic.ncbs.claim.feign.mesh.TpaGlobalRequest;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.investigate.TpaGlobalAgentDTO;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.vo.quality.QualityTaskRequestVO;
import com.paic.ncbs.claim.model.vo.quality.QualityrequstVO;
import com.paic.ncbs.claim.model.vo.quality.Reportinfo;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;
import com.paic.ncbs.claim.model.vo.user.DepartmentUserVO;
import com.paic.ncbs.claim.service.qualitychecke.ClmsQualityInfoService;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimQuanlityCaseVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import com.paic.ncbs.claim.service.qualitychecke.ClmsQualityRecordService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.claim.service.user.DepartmentService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserGradeInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 案件质检信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
@RestController
@RequestMapping("/qualitychecke")
public class ClmsQualityInfoController {
    @Autowired
    private ClmsQualityInfoService clmsQualityInfoService;
    @Autowired
    private TpaGlobalRequest tpaGlobalRequest;
    @Autowired
    TaskPoolService taskPoolService;
    @Autowired
    CacheService cacheService;
    @Autowired
    private ImportQualityRecordMapper importQualityRecordMapper;
    @Autowired
    private ClmsQualityRecordService clmsQualityRecordService;
    @Autowired
    private DepartmentService departmentServcie;
    @Autowired
    DepartmentDefineService departmentDefineService;

    /**
     * 质检任务案件查询接口
     *
     * @param qualityrequstVO 质检信息实体
     * @return 是否生成成功
     */
    @PostMapping("/reportfind")
    @ApiOperation("质检任务案件查询接口")
    public ResponseResult<List<WorkBenchTaskVO>> reportfind(@RequestBody ClaimQuanlityCaseVO qualityrequstVO) {
        return ResponseResult.success(clmsQualityInfoService.getWorkBenchTaskList(qualityrequstVO));
    }

    /**
     * 质检任务生成接口
     *
     * @param request 质检信息实体
     * @return 是否生成成功
     */
    @PostMapping("/generate")
    @ApiOperation("质检任务生成接口")
    public ResponseResult generateQualityTask(@RequestBody QualityTaskRequestVO request) {
        try {
            if (!generateBatchNo(WebServletContext.getUserId())) {
                return ResponseResult.fail("999999", "当前登录的用户没有操作权限");
            }
            // 获取案件信息列表和质检员信息列表
            List<Reportinfo> reportInfoList = request.getReportInfoList();
            List<QualityrequstVO> qualityRequestList = request.getQualityRequestList();
            if (reportInfoList == null || reportInfoList.isEmpty()) {
                return ResponseResult.fail("999999", "没有可分配的案件信息");
            }

            if (qualityRequestList == null || qualityRequestList.isEmpty()) {
                return ResponseResult.fail("999999", "没有质检员信息");
            }
            QualityrequstVO qualityRequests = new QualityrequstVO();
            String message = null;
            // 分配案件给质检员
            int reportIndex = 0;
            // 第一轮分配：按指定任务数分配
            for (QualityrequstVO qualityRequest : qualityRequestList) {
                Integer taskCount = qualityRequest.getSum();
                if (taskCount == null) {
                    taskCount = 0;
                }
                // 为当前质检员分配指定数量的案件
                for (int i = 0; i < taskCount && reportIndex < reportInfoList.size(); i++) {
                    Reportinfo reportInfo = reportInfoList.get(reportIndex);

                    // 封装质检任务对象
                    QualityrequstVO taskVO = new QualityrequstVO();
                    taskVO.setReportNo(reportInfo.getReportNo());
                    taskVO.setCaseTimes(reportInfo.getCaseTimes());
                    taskVO.setPerson(clmsQualityInfoService.getUserName(qualityRequest.getPersonno()));
                    taskVO.setPersonno(qualityRequest.getPersonno());
                    taskVO.setCompany(qualityRequest.getCompany());

                    // 调用服务层生成单个质检任务
                    message = clmsQualityInfoService.insertQualityInfo(taskVO);

                    reportIndex++;
                }
            }
            // 处理剩余的案件（如果有）
            while (reportIndex < reportInfoList.size()) {
                for (QualityrequstVO qualityRequest : qualityRequestList) {
                    if (reportIndex >= reportInfoList.size()) {
                        break;
                    }

                    Reportinfo reportInfo = reportInfoList.get(reportIndex);

                    // 封装质检任务对象
                    QualityrequstVO taskVO = new QualityrequstVO();
                    taskVO.setReportNo(reportInfo.getReportNo());
                    taskVO.setCaseTimes(reportInfo.getCaseTimes());
                    taskVO.setPerson(qualityRequest.getPerson());
                    taskVO.setPersonno(qualityRequest.getPersonno());
                    taskVO.setCompany(qualityRequest.getCompany());

                    // 调用服务层生成单个质检任务
                    message = clmsQualityInfoService.insertQualityInfo(taskVO);

                    reportIndex++;
                }
            }
            if (message == null) {
                return ResponseResult.success(message);
            } else {
                return ResponseResult.fail("999999", message);
            }

        } catch (Exception e) {
            LogUtil.error("生成质检任务失败", e);
            return ResponseResult.fail("999999", "生成质检任务失败: " + e.getMessage());
        }
    }


    /**
     * 再次质检接口
     *
     * @param qualityrequstVO 质检信息实体
     * @return 是否修改成功
     */
    @PutMapping("/recreater")
    @ApiOperation("再次质检接口")
    public ResponseResult recreater(@RequestBody QualityrequstVO qualityrequstVO) {
        if (!generateBatchNo(WebServletContext.getUserId())) {
            return ResponseResult.fail("999999", "当前登录的用户没有操作权限");
        }
        Short qualityTimes = qualityrequstVO.getCaseTimes() != null ? qualityrequstVO.getCaseTimes().shortValue() : 1;
        ClmsQualityInfo clmsQualityInfos = clmsQualityInfoService.selectQualityInfoWithMaxQiserNo(qualityrequstVO.getReportNo(), qualityTimes);
        String quality =null;
        if (clmsQualityInfos != null) {
            if ("1".equals(clmsQualityInfos.getIsEnd())) {
                quality = clmsQualityInfoService.insertQualityInfo(qualityrequstVO);
                if (quality != null) {
                    return ResponseResult.fail("999999", quality);
                }
            } else {
                return ResponseResult.fail("999999", "当前案件有质检任务未结束，不能发起再次质检");
            }
        }else{
            return ResponseResult.fail("999999", "数据异常");
        }
        return ResponseResult.success(quality);
    }

    /**
     * 质检信息修改接口
     *
     * @param clmsQualityInfo 质检信息实体
     * @return 是否修改成功
     */
    @PutMapping("/save")
    @ApiOperation("质检信息修改接口")
    public ResponseResult updateQualityInfo(@RequestBody QualityInfoDetailVO clmsQualityInfo) {
        try {
            // 参数校验
            if (clmsQualityInfo == null) {
                return ResponseResult.fail("999999", "请求参数不能为空");
            }

            if (StringUtils.isEmptyStr(clmsQualityInfo.getSubmitflag())) {
                return ResponseResult.fail("999999", "操作类型不能为空");
            }

            // 执行更新操作
            boolean result = clmsQualityInfoService.updateQualityInfo(clmsQualityInfo);

            // 检查操作结果
            if (result) {
                return ResponseResult.success();
            } else {
                if ("update".equals(clmsQualityInfo.getSubmitflag())) {
                    return ResponseResult.fail("999999", "质检信息保存失败");
                } else {
                    return ResponseResult.fail("999999", "质检信息提交失败");
                }
            }
        } catch (Exception e) {
            LogUtil.error("质检信息修改失败，操作类型: " + clmsQualityInfo.getSubmitflag(), e);
            return ResponseResult.fail("999999", "质检信息修改失败: " + e.getMessage());
        }
    }

    /**
     * 根据人员id查询质检任务接口
     *
     * @param qinspector 质检员ID
     * @return 质检任务列表
     */
    @GetMapping("/queryByInspector/{qinspector}")
    @ApiOperation("根据质检员ID查询质检任务接口")
    public ResponseResult<List<ClmsQualityInfo>> queryQualityTasksByInspector(@PathVariable String qinspector) {
        return ResponseResult.success(clmsQualityInfoService.selectAllQualityInfo(qinspector));
    }

    /**
     * 查询历史质检任务接口
     *
     * @param requestParams
     * @return 质检历史任务列表
     */
    @PostMapping("/query")
    @ApiOperation("根据报案号或者质检任务号查询质检任务接口")
    public ResponseResult<List<ClmsQualityInfo>> queryQualityTasks(@RequestBody Map<String, String> requestParams) {
        String reportNo = requestParams.get("reportNo");
        String caseTimes = requestParams.get("caseTimes");
        ClmsQualityInfo condition = new ClmsQualityInfo();
        if (reportNo != null && !reportNo.isEmpty()) {
            condition.setReportNo(reportNo);
        }
        if (caseTimes != null && !caseTimes.isEmpty()) {
            condition.setCaseTimes(Short.valueOf(caseTimes));
        }
        List<ClmsQualityInfo> clmsQualityInfoList = clmsQualityInfoService.selectQualityInfoByCondition(condition);
        return ResponseResult.success(clmsQualityInfoList);
    }

    /**
     * 根据报案号和赔付次数更新质检信息接口(审核接口)
     *
     * @param clmsQualityInfo 质检信息实体
     * @return 是否更新成功
     */
    @PutMapping("/updateByReportNoAndCaseTimes")
    @ApiOperation("审核提交接口")
    public ResponseResult updateQualityInfoByReportNoAndCaseTimes(@RequestBody ClmsQualityInfo clmsQualityInfo) {

        String errorMsg = clmsQualityInfoService.updateQualityInfoByReportNoAndCaseTimes(clmsQualityInfo);
        if (errorMsg == null || errorMsg.isEmpty()) {
            return ResponseResult.success();
        } else {
            return ResponseResult.fail("999999", errorMsg);
        }
    }

    /**
     * 查询质检结果接口
     *
     * @param queryVO 查询条件
     * @return 质检任务列表
     */
    @PostMapping("/queryByConditions")
    @ApiOperation("根据多种条件查询质检任务接口")
    public ResponseResult<List<QualityInfoWithDetailVO>> queryQualityTasksByConditions(@RequestBody QualityQueryVO queryVO) {
        List<QualityInfoWithDetailVO> clmsQualityInfoList = clmsQualityInfoService.selectQualityInfoByConditions(queryVO);
        return ResponseResult.success(clmsQualityInfoList);
    }

    /**
     * 关联详情表查询整案质检信息
     *
     * @param queryVO 查询条件
     * @return 质检任务列表
     */
    @PostMapping("/queryQualityInfoDetail")
    @ApiOperation("根据多种条件查询质检任务接口")
    public ResponseResult<Object> selectQualityInfoDetailByCondition(@RequestBody QualityrequstVO queryVO) {
        if (!generateBatchNo(WebServletContext.getUserId())) {
            return ResponseResult.fail("999999", "当前登录的用户没有操作权限");
        }
        Short caseTimes = queryVO.getCaseTimes() != null ? queryVO.getCaseTimes().shortValue() : null;
        return ResponseResult.success(clmsQualityInfoService.selectQualityInfoDetailByCondition(queryVO.getReportNo(), caseTimes, queryVO.getQualityno()));
    }

    /**
     * 导入质检信息Excel文件接口
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/importExcel")
    @ApiOperation("导入质检信息Excel文件接口")
    public ResponseResult importQualityInfoExcel(@RequestParam("file") MultipartFile file, @RequestParam String isglobal) {
        InputStream inputStream = null;
        try {
            // 检查文件是否为空
            if (file.isEmpty()) {
                return ResponseResult.fail("999999", "上传文件不能为空");
            }

            // 检查文件大小
            if (file.getSize() == 0) {
                return ResponseResult.fail("999999", "上传文件大小为0");
            }

            // 检查文件扩展名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename != null && !originalFilename.toLowerCase().endsWith(".xlsx")
                    && !originalFilename.toLowerCase().endsWith(".xls")) {
                return ResponseResult.fail("999999", "文件格式不正确，请上传.xlsx或.xls格式的Excel文件");
            }

            // 获取文件输入流
            inputStream = file.getInputStream();
            List<ClmsQualityInfo> importList;

            try {
                // 尝试读取Excel文件，先尝试xlsx格式
                try {
                    importList = EasyExcel.read(inputStream)
                            .excelType(com.alibaba.excel.support.ExcelTypeEnum.XLSX)
                            .head(ClmsQualityInfo.class)
                            .sheet()
                            .doReadSync();
                } catch (ExcelCommonException e) {
                    // 如果xlsx格式失败，尝试xls格式
                    try {
                        inputStream.close(); // 关闭之前的流
                        inputStream = file.getInputStream(); // 重新获取输入流
                        importList = EasyExcel.read(inputStream)
                                .excelType(com.alibaba.excel.support.ExcelTypeEnum.XLS)
                                .head(ClmsQualityInfo.class)
                                .sheet()
                                .doReadSync();
                    } catch (Exception innerException) {
                        // 如果两种格式都失败，返回更具体的错误信息
                        LogUtil.error("无法读取Excel文件，可能是文件被保护或格式不正确", innerException);
                        return ResponseResult.fail("999999", "无法读取Excel文件，请确保文件未被保护且为标准Excel格式。如文件图标有盾牌标记，请右键文件->属性->解除锁定");
                    }
                }
            } catch (Exception e) {
                LogUtil.error("读取Excel文件异常", e);
                return ResponseResult.fail("999999", "读取Excel文件失败，请检查文件是否被保护或格式是否正确: " + e.getMessage());
            }

            // 检查是否有数据
            if (importList == null || importList.isEmpty()) {
                return ResponseResult.fail("999999", "Excel文件中没有数据");
            }
            // 转换为ClmsQualityInfo对象并保存
            int sum = 0;
            String batchNo = this.generateBatchNo();
            Map<String, Object> resultData = new HashMap<>();
            List<ImportQualityRecord> importQualityRecordList = new ArrayList<>();
            for (ClmsQualityInfo importVO : importList) {
                String message = null;
                ImportQualityRecord importQualityRecord = new ImportQualityRecord();
                QualityrequstVO qualityrequstVO = new QualityrequstVO();
                qualityrequstVO.setBatchNo(batchNo);
                qualityrequstVO.setPersonno(importVO.getHandler());
                qualityrequstVO.setPerson(clmsQualityInfoService.getUserName(importVO.getHandler()));
                int lastIndex = importVO.getReportNo().lastIndexOf('_');
                String reportNo = importVO.getReportNo();
                String caseTimes = "1";
                // 如果找到了"_"且后面还有字符
                if (lastIndex >= 0 && lastIndex < importVO.getReportNo().length() - 1) {
                    reportNo = importVO.getReportNo().substring(0, lastIndex);
                    caseTimes = importVO.getReportNo().substring(lastIndex + 1);
                }
                importQualityRecord.setId(UuidUtil.getUUID());
                importQualityRecord.setBatchNo(qualityrequstVO.getBatchNo());
                importQualityRecord.setQinitiator(importVO.getHandler());
                importQualityRecord.setReportNo(reportNo+"_"+caseTimes);
                importQualityRecord.setImportTime(LocalDateTime.now());
                importQualityRecord.setCreatedBy(WebServletContext.getUserId());
                importQualityRecord.setUpdatedBy(WebServletContext.getUserId());
                qualityrequstVO.setReportNo(reportNo);
                qualityrequstVO.setCaseTimes(Integer.valueOf(caseTimes));
                qualityrequstVO.setIsglobal(isglobal);
                if (!generateBatchNo(importVO.getHandler())) {
                    message = "当前导入的用户没有质检权限或用户不存在";
                    resultData.put(qualityrequstVO.getReportNo(), message);
                    importQualityRecord.setTaskStatus("0");
                    importQualityRecord.setFailReason(message);
                }else{
                    UserInfoDTO userInfoDTO = cacheService.queryUserInfo(importVO.getHandler());
                    //当前登录人员的机构
                    String currentDeptCode = departmentServcie.getTopDepartment(WebServletContext.getDepartmentCode());
                    if(!StringUtils.isNotEmpty(message) && userInfoDTO!=null){
                        //清单导入人员的机构
                        String importDeptCode = departmentServcie.getTopDepartment(userInfoDTO.getComCode());
                        if(!currentDeptCode.equals(importDeptCode)){
                            message = "当前导入的用户和发起质检人员机构不匹配";
                            resultData.put(qualityrequstVO.getReportNo(), message);
                            importQualityRecord.setTaskStatus("0");
                            importQualityRecord.setFailReason(message);
                        }else{
                            // 保存到数据库
                            message = clmsQualityInfoService.insertQualityInfo(qualityrequstVO);
                        }
                    }

                    if (message != null) {
                        importQualityRecord.setTaskStatus("0");
                        if(!StringUtils.isNotEmpty(message)) {
                            importQualityRecord.setFailReason(message);
                        }
                        resultData.put(qualityrequstVO.getReportNo(), message);
                    } else {
                        importQualityRecord.setTaskStatus("1");
                        importQualityRecord.setFailReason("成功");
                        resultData.put(qualityrequstVO.getReportNo(), "成功");
                        sum++;
                    }
                }
                importQualityRecordList.add(importQualityRecord);
            }
            importQualityRecordMapper.qualityRecordBatchInsert(importQualityRecordList);
            int fail = importList.size() - sum;
            resultData.put("message", "成功导入" + sum + "条记录,失败" + fail + "条");
            resultData.put("batchNo", batchNo);
            return ResponseResult.success(resultData);
        } catch (Exception e) {
            LogUtil.error("导入质检信息失败", e);
            return ResponseResult.fail("999999", "导入失败: " + e.getMessage());
        } finally {
            // 确保输入流被关闭
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    LogUtil.warn("关闭文件输入流失败", e);
                }
            }
        }
    }


    @PostMapping("/importExcelResult")
    @ApiOperation("导入质检信息结果查询接口")
    public ResponseResult<Object> importExcelResult(@RequestBody Map<String, String> request) {
        String batchNo = request.get("batchNo");
        String qinitiator = request.get("qinitiator");
        if (StringUtils.isEmptyStr(qinitiator)) {
            qinitiator = WebServletContext.getUserId();
        }
        List<ImportQualityRecord> ClmsQualityInfo = importQualityRecordMapper.selectQualityInfoByBatch(batchNo, qinitiator);
        ClmsQualityInfo = ClmsQualityInfo.stream()
                .map(record -> {
                    if (record != null && record.getTaskStatus() != null) {
                        if ("0".equals(record.getTaskStatus())) {
                            record.setTaskStatus("失败");
                        } else if ("1".equals(record.getTaskStatus())) {
                            record.setTaskStatus("成功");
                        }
                    }
                    return record;
                })
                .collect(Collectors.toList());
        return ResponseResult.success(ClmsQualityInfo);
    }

    /**
     * 导出质检信息Excel文件接口
     *
     * @param request  查询条件列表
     * @param response 响应对象
     */
    @PostMapping("/exportExcel")
    @ApiOperation("导出质检信息Excel文件接口")
    public void exportQualityInfoExcel(@RequestBody Map<String, Object> request,
                                       HttpServletResponse response) {
        OutputStream out = null;
        try {
            // 参数校验
            if (request == null) {
                LogUtil.error("导出案件信息Excel文件失败：请求参数为空");
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "请求参数不能为空");
                return;
            }

            // 从请求报文中获取导出类型和查询条件
            String exportType = (String) request.get("exportType");
            List<Map<String, Object>> queryList = (List<Map<String, Object>>) request.get("queryList");

            if ("quality".equals(exportType)) {
                // 查询质检数据
                List<QualityInfoWithDetailVO> qualityInfoList = queryList.stream().flatMap(map -> {
                    QualityQueryVO condition = new QualityQueryVO();
                    if (map.get("reportNo") != null) {
                        condition.setReportNo((String) map.get("reportNo"));
                    }
                    if (map.get("caseTimes") != null) {
                        condition.setCaseTimes(Short.valueOf(map.get("caseTimes").toString()));
                    }
                    if (map.get("id") != null) {
                        condition.setId(map.get("id").toString());
                    }
                    return clmsQualityInfoService.selectQualityInfoByConditions(condition).stream();
                }).collect(Collectors.toList());

                // 转换为导出VO
                List<QualityInfoExportVO> exportList = qualityInfoList.stream().map(info -> {
                    QualityInfoExportVO exportVO = new QualityInfoExportVO();
                    exportVO.setSerialNo(info.getSerialNo());
                    // 报案号拼接赔付次数
                    exportVO.setReportNoWithCaseTimes(info.getReportNo() + "_" + info.getCaseTimes());
                    //exportVO.setRegistNo(info.getRegistNo());
                    exportVO.setHandleCom(info.getHandleCom());
                    exportVO.setProductName(info.getProductName());
                    // 日期时间字段转换为字符串
                    exportVO.setCloseDate(QualityInfoExportVO.formatDateTime(info.getCloseDate()));
                    exportVO.setQinitiator(info.getQinitiator());
                    exportVO.setStartTime(QualityInfoExportVO.formatDateTime(info.getStartTime()));
                    exportVO.setEndTime(QualityInfoExportVO.formatDateTime(info.getEndTime()));
                    exportVO.setQinspector(info.getQinspector());

                    // 质检结果处理
                    if ("0".equals(info.getCaseNature())) {
                        exportVO.setQualityResult("正常案件");
                    } else if ("1".equals(info.getCaseNature())) {
                        exportVO.setQualityResult("疑义案件");
                    } else if ("2".equals(info.getCaseNature())) {
                        exportVO.setQualityResult("问题案件");
                    }
                    // 质检环节和质检规范
                    exportVO.setInspnStage(info.getInspnStage());
                    exportVO.setInspnStandard(info.getInspnStandard());

                    // 差错等级转换
                    String errorLevel = info.getErrorLevel();
                    if ("0".equals(errorLevel)) {
                        exportVO.setErrorLevelDesc("低");
                    } else if ("1".equals(errorLevel)) {
                        exportVO.setErrorLevelDesc("中");
                    } else if ("2".equals(errorLevel)) {
                        exportVO.setErrorLevelDesc("高");
                    } else {
                        exportVO.setErrorLevelDesc(errorLevel);
                    }

                    exportVO.setDiffAmount(info.getDiffAmount());
                    exportVO.setLossAmount(info.getLossAmount());
                    exportVO.setPaymentAmount(info.getPaymentAmount());

                    // 质检是否结束转换
                    String isEnd = info.getIsEnd();
                    if ("0".equals(isEnd)) {
                        exportVO.setIsEndDesc("未结束");
                    } else if ("1".equals(isEnd)) {
                        exportVO.setIsEndDesc("已结束");
                    } else {
                        exportVO.setIsEndDesc(isEnd);
                    }

                    // 任务状态转换
                    String taskStatus = info.getTaskStatus();
                    if ("0".equals(taskStatus)) {
                        exportVO.setTaskStatusDesc("未处理");
                    } else if ("1".equals(taskStatus)) {
                        exportVO.setTaskStatusDesc("处理中");
                    } else if ("2".equals(taskStatus)) {
                        exportVO.setTaskStatusDesc("处理结束");
                    } else {
                        exportVO.setTaskStatusDesc(taskStatus);
                    }

                    return exportVO;
                }).collect(Collectors.toList());

                // 设置响应头
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf-8");
                response.setHeader("Content-Disposition", "attachment; filename=质检信息.xlsx");

                // 导出Excel
                EasyExcel.write(response.getOutputStream(), QualityInfoExportVO.class)
                        .sheet("质检信息")
                        .doWrite(exportList);
            } else if ("report".equals(exportType)) {
                // 查询案件数据（默认）
                List<CaseInfoExportVO> exportList = new ArrayList<>();
                for (Map<String, Object> map : queryList) {
                    String reportNo = (String) map.get("reportNo");
                    Object caseTimesObj = map.get("caseTimes");
                    Short caseTimes = null;

                    if (caseTimesObj != null) {
                        try {
                            caseTimes = Short.valueOf(caseTimesObj.toString());
                        } catch (NumberFormatException e) {
                            LogUtil.warn("caseTimes转换失败: " + caseTimesObj);
                        }
                    }

                    // 直接查询CLM_WHOLE_CASE_BASE表数据
                    WholeCaseBaseDTO condition = new WholeCaseBaseDTO();
                    if (reportNo != null && !reportNo.isEmpty()) {
                        condition.setReportNo(reportNo);
                    }
                    if (caseTimes != null) {
                        condition.setCaseTimes(caseTimes);
                    }
                    if (StringUtils.isNotEmpty(condition.getReportNo())) {
                        CaseInfoExportVO exportVO = new CaseInfoExportVO();
                        // 报案号拼接赔付次数
                        exportVO.setReportNoWithCaseTimes(condition.getReportNo() + "_" + condition.getCaseTimes());
                        // 质检人员工号无需赋值，保持默认null即可
                        exportList.add(exportVO);
                    }
                }

                // 设置响应头
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf-8");
                String fileName = "案件信息.xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

                // 导出Excel
                out = response.getOutputStream();
                EasyExcel.write(out, CaseInfoExportVO.class)
                        .sheet("案件信息")
                        .doWrite(exportList);
            }

        } catch (Exception e) {
            LogUtil.error("导出Excel文件失败", e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                LogUtil.error("发送错误响应失败", ioException);
            }
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    LogUtil.error("关闭输出流失败", e);
                }
            }
        }
    }

    /**
     * 下载质检信息模板文件
     *
     * @param response 响应对象
     */
    @GetMapping("/downloadTemplate")
    @ApiOperation("下载质检信息模板文件")
    public void downloadTemplate(HttpServletResponse response) {
        BufferedOutputStream out = null;
        try {
            //获取输入流，原始模板位置
            InputStream bis = this.getClass().getResourceAsStream("/templates/质检导入模板.xlsx");
            //转码，免得文件名中文乱码
            String fileName = "质检导入模板.xlsx";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            //设置文件下载头
            response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
            //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            out = new BufferedOutputStream(response.getOutputStream());
            int len = 0;
            while ((len = bis.read()) != -1) {
                out.write(len);
                out.flush();
            }
        } catch (IOException e) {
            LogUtil.error("下载文件异常", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                LogUtil.error("流关闭异常", e);
            }
        }
    }

    /**
     * 批量删除质检任务接口（包括质检信息和详情）
     *
     * @param ids 质检任务ID列表
     * @return 删除结果
     */
    @DeleteMapping("/deleteTask")
    @ApiOperation("批量删除质检任务接口")
    public ResponseResult deleteQualityTask(@RequestBody List<String> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return ResponseResult.fail("999999", "请至少选择一个质检任务");
            }

            int successCount = 0;
            int failCount = 0;
            List<String> failIds = new ArrayList<>();
            String result = "";
            for (String id : ids) {
                try {
                    result = clmsQualityInfoService.deleteQualityInfoAndDetailById(id);
                    if ("true".equals(result)) {
                        successCount++;
                    } else {
                        failCount++;
                        failIds.add(result);
                    }
                } catch (Exception e) {
                    LogUtil.error("删除质检任务失败，ID: " + id, e);
                    failCount++;
                    failIds.add(result);
                }
            }

            if (failCount == 0) {
                return ResponseResult.success("成功删除" + successCount + "个质检任务");
            } else {
                return ResponseResult.fail("999999", "删除完成，成功" + successCount + "个，失败" + failCount + "个。失败案件: " + failIds);
            }
        } catch (Exception e) {
            LogUtil.error("批量删除质检任务失败", e);
            return ResponseResult.fail("999999", "批量删除质检任务失败: " + e.getMessage());
        }
    }

    public String generateBatchNo() {
        try {
            // 获取当前日期
            LocalDateTime now = LocalDateTime.now();
            // 格式化日期为YYYYMMDD
            String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            // 构造批次号前缀
            String prefix = "BZ" + dateStr;
            // 查询当天已生成的最大序号
            String maxBatchNo = importQualityRecordMapper.selectMaxBatchNoByDate(prefix + "%");
            int sequence = 1;
            if (maxBatchNo != null && !maxBatchNo.isEmpty()) {
                // 从最大批次号中提取序号部分并加1
                String sequenceStr = maxBatchNo.substring(prefix.length());
                try {
                    sequence = Integer.parseInt(sequenceStr) + 1;
                } catch (NumberFormatException e) {
                    // 如果解析失败，使用默认序号1
                    sequence = 1;
                }
            }
            // 格式化序号为5位数字，不足补0
            String sequenceStr = String.format("%05d", sequence);
            // 拼接完整批次号
            String batchNo = prefix + sequenceStr;
            return batchNo;
        } catch (Exception e) {
            return "生成批次号失败: " + e.getMessage();
        }
    }

    /**
     * 获取服务商信息列表接口
     *
     * @return 服务商信息列表
     */
    @GetMapping("/serverInfoList")
    @ApiOperation("获取服务商信息列表接口")
    public ResponseResult getServerInfoList() {
        try {
            TpaGlobalAgentDTO tpaGlobalAgentDTO = new TpaGlobalAgentDTO();
            if (tpaGlobalAgentDTO.getRequestData() == null) {
                TpaGlobalAgentDTO.TpaRequestData tpaRequestData = tpaGlobalAgentDTO.createTpaRequestData();
                tpaRequestData.setSupplierTypeCode("04");
                tpaGlobalAgentDTO.setRequestData(tpaRequestData);
            }
            tpaGlobalAgentDTO.setRequestTime("" + System.currentTimeMillis());
            tpaGlobalAgentDTO.setRequestId(MDC.get("requestId"));
            tpaGlobalAgentDTO.setRequestType("FST-008");
            tpaGlobalAgentDTO.setCompanyId("tpa-dev");

            String result = tpaGlobalRequest.getExternalDepartmentList(tpaGlobalAgentDTO);
            Map<String, Object> resultMap = JsonUtils.jsonToMap(result);
            log.info("获取服务商信息列表接口返回结果：{}", result);
            // 创建用于返回的供应商名称列表
            List<String> supplierNames = new ArrayList<>();

            // 从responseData中获取supplierInfoList
            Object responseDataObj = resultMap.get("responseData");
            if (responseDataObj instanceof Map) {
                Map<String, Object> responseData = (Map<String, Object>) responseDataObj;
                Object supplierInfoListObj = responseData.get("supplierInfoList");

                if (supplierInfoListObj instanceof List) {
                    List<Map<String, Object>> supplierInfoList = (List<Map<String, Object>>) supplierInfoListObj;
                    for (Map<String, Object> supplierInfo : supplierInfoList) {
                        String supplierName = (String) supplierInfo.get("supplierName");
                        if (supplierName != null && !supplierName.isEmpty()) {
                            supplierNames.add(supplierName);
                        }
                    }
                }
            }

            // 封装到JSON对象中
            Map<String, Object> response = new HashMap<>();
            response.put("supplierNames", supplierNames);
            //response.put("count", supplierNames.size());

            return ResponseResult.success(response);
        } catch (Exception e) {
            LogUtil.error("获取服务商信息列表失败", e);
            return ResponseResult.fail("999999", "获取服务商信息列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/findUpperDepartment")
    @ApiOperation("查询上级机构")
    public ResponseResult findUpperDepartment(@RequestBody Map<String, String> request) throws NcbsException {
        //因为顶级机构的上级机构是其本身 无法递归终止 要排除
        String handler = request.get("handler");
        List<UserInfoDTO> userInfoDTO = new ArrayList<>();
        DepartmentDTO parentDep = departmentDefineService.queryParentDepartmentInfoByDeptCode(WebServletContext.getDepartmentCode());
        if (parentDep != null) {
            userInfoDTO = taskPoolService.searchTaskDealUser(parentDep.getUpperDepartmentCode(), "OC_QUALITY_CHECK_REVIEW");
        }
        return ResponseResult.success(userInfoDTO);
    }

    public boolean generateBatchNo(String userCode) {
        List<UserGradeInfoDTO> userGradeInfoss = null;
        try {
            if( StringUtils.isEmptyStr(userCode)){
                return false;
            }
            UserInfoDTO userInfoDTO = cacheService.queryUserInfo(userCode);
            if(userInfoDTO != null){
                userGradeInfoss = cacheService.queryUserGradeList(userCode, userInfoDTO.getComCode());
                if (userGradeInfoss != null && !userGradeInfoss.isEmpty()) {
                    for (UserGradeInfoDTO userGradeInfo : userGradeInfoss) {
                        if ("CLAIM-NEW-3".equals(userGradeInfo.getGradeCode()) || "CLAIM-NEW-1".equals(userGradeInfo.getGradeCode())) {
                            return true;
                        }
                    }
                }
            }else{
                return false;
            }

        } catch (NcbsException e) {
            return false;
        }
        return false;
    }

    /**
     * 根据报案号和赔付次数查询质检轨迹记录接口
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 质检轨迹记录列表
     */
    /**
     * 根据报案号和赔付次数查询质检轨迹记录接口
     *
     * @param request 查询条件
     * @return 质检轨迹记录列表
     */
    @PostMapping("/qualityRecord")
    @ApiOperation("根据报案号和赔付次数查询质检轨迹记录接口")
    public ResponseResult<List<ClmsQualityRecord>> queryQualityRecordByReportNoAndCaseTimes(
            @RequestBody Map<String, Object> request) {
        try {

            String reportNo = (String) request.get("reportNo");
            Short caseTimes = null;
            String id = (String) request.get("id");
            if (request.get("caseTimes") != null) {
                if (request.get("caseTimes") instanceof Short) {
                    caseTimes = (Short) request.get("caseTimes");
                } else {
                    try {
                        caseTimes = Short.valueOf(request.get("caseTimes").toString());
                    } catch (NumberFormatException e) {
                        LogUtil.error("查询质检轨迹记录失败: " + e.getMessage(), e);
                    }
                }
            }
            List<ClmsQualityRecord> qualityRecords = clmsQualityRecordService.selectByReportNoAndCaseTimes(reportNo, caseTimes,id);
            return ResponseResult.success(qualityRecords);
        } catch (Exception e) {
            LogUtil.error("查询质检轨迹记录失败: " + e.getMessage(), e);
        }
        return ResponseResult.success(null);
    }

}
