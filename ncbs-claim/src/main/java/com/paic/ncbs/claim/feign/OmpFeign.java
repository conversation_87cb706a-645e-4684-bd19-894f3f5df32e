package com.paic.ncbs.claim.feign;

import com.paic.ncbs.claim.model.dto.estimate.RepeatReceiptDTO;
import com.paic.ncbs.claim.model.vo.estimate.RepeatReceiptVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Description feign 调用omp控制层
 * @date 2023-06-29 11:23
 */
@FeignClient(name = "ncbs-omp")
@Component
public interface OmpFeign {

    @PostMapping(value="omp/claim/riskcontrolplatform/checkRepeatReceipt")
    String checkRepeatReceipt(@RequestBody List<RepeatReceiptDTO> vos);
}
