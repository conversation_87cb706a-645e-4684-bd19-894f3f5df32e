package com.paic.ncbs.claim.model.dto.problem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "问题件答复业务数据")
public class RequestData {

    /**
     * 报案号
     */
    @ApiModelProperty(value = "报案号")
    private String registNo;

    /**
     * 保单号 非必传
     */
    @ApiModelProperty(value = "保单号")
    private String agrmNo;

    /**
     * 立案号 非必传
     */
    @ApiModelProperty(value = "立案号")
    private String claimNo;

    /**
     * 归属机构代码 非必传
     */
    @ApiModelProperty(value = "归属机构代码")
    private String comCode;

    /**
     * 险种代码 非必传
     */
    @ApiModelProperty(value = "险种代码")
    private String prdCode;

    /**
     * 方案代码 非必传
     */
    @ApiModelProperty(value = "方案代码")
    private String objectCode;

    /**
     * 问题件编号
     */
    @ApiModelProperty(value = "问题件编号")
    private String problemNo;

    /**
     * 问题件类型
     */
    @ApiModelProperty(value = "问题件类型")
    private String problemType;

    /**
     * 案件结论
     */
    @ApiModelProperty(value = "案件结论")
    private String caseConclusion;

    /**
     * 案件结论明细
     */
    @ApiModelProperty(value = "案件结论明细")
    private String caseConclusionDetail;

    /**
     * 问题件回复时间
     */
    @ApiModelProperty(value = "问题件回复时间")
    private String replyTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    private String dealUser;

    /**
     * 赔款金额
     */
    @ApiModelProperty(value = "赔款金额")
    private BigDecimal payAmount;
}
