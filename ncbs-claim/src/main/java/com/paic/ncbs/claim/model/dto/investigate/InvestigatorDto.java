package com.paic.ncbs.claim.model.dto.investigate;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("调查人DTO")
public class InvestigatorDto {
    @ApiModelProperty(value = "调查人(UM)")
    private String investigatorUm;
    @ApiModelProperty(value = "是否为主调查")
    private String isPrimary;

    @ApiModelProperty(value = "调查人姓名")
    private String investigatorUmName;
}