package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.dao.entity.checkloss.BaseRimsOpencoverTreatyEntity;

public interface BaseRimsOpencoverTreatyEntityMapper {

    int deleteByPrimaryKey(String idRimsOpencoverTreaty);

    int insert(BaseRimsOpencoverTreatyEntity record);

    int insertSelective(BaseRimsOpencoverTreatyEntity record);

    BaseRimsOpencoverTreatyEntity selectByPrimaryKey(String idRimsOpencoverTreaty);

    int updateByPrimaryKeySelective(BaseRimsOpencoverTreatyEntity record);

    int updateByPrimaryKey(BaseRimsOpencoverTreatyEntity record);

    BaseRimsOpencoverTreatyEntity getInfoByTreatyNo(String treatyCode);
}