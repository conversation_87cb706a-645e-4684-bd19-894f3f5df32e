package com.paic.ncbs.claim.dao.entity.risk;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 就医轨迹明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
@Getter
@Setter
@TableName("clms_medical_record_details")
public class MedicalRecordDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 就医轨迹主键
     */
    @TableField("medical_record_id")
    private String medicalRecordId;

    /**
     * 就诊类型
     */
    @TableField("medical_type")
    private String medicalType;

    /**
     * 就诊时间
     */
    @TableField("medical_time")
    private String medicalTime;

    /**
     * 就诊医院
     */
    @TableField("hospital")
    private String hospital;

    /**
     * 就诊花费
     */
    @TableField("cost_amount")
    private String costAmount;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人员
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;
}
