package com.paic.ncbs.claim.dao.mapper.doc;

import com.paic.ncbs.claim.model.dto.doc.DocumentTypeDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.fileupload.SelfDocTypeDTO;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import org.apache.ibatis.annotations.Param;
import org.apache.tomcat.jni.FileInfo;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface DocumentTypeMapper {

    List<DocumentTypeDTO> getDocumentSmallTypeList();

    List<DocumentTypeDTO> getDocumentBigTypeList();

    List<SelfDocTypeDTO> getSelfTypeByScene(@Param("scene") String scene);

    String getBigCodeBySmallCode(@Param("smallCode") String smallCode);

    String getBigNameBySmallCode(@Param("smallCode") String smallCode);

    String getSmallNameBySmallCode(@Param("smallCode") String smallCode);

    /**
     * 通过报案号，赔付次数查询单证类型
     * @param fileInfoDTO
     * @param fileInfoDTO
     */
    List<FileInfoDTO> getDocumentListInfo(FileInfoDTO fileInfoDTO);
}
