create table CLMS_HOSPITAL_PACKAGE (
   id int not null primary key auto_increment comment '主键',
   package_name varchar(100) not null comment '模板包名称',
   hostitals json comment '包含医院',
   valid_flag boolean default false comment '有效标志',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   index idx_package_name (package_name),
   index idx_valid_flag (valid_flag),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='医院模板包表';

create table CLMS_MEDICINE_PACKAGE (
   id int not null primary key auto_increment comment '主键',
   package_name varchar(100) not null comment '模板包名称',
   package_type varchar(1) not null comment '药品模板分类',
   valid_flag boolean comment '有效标志',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   index idx_package_name (package_name),
   index idx_package_type (package_type),
   index idx_valid_flag (valid_flag),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='药品模板包表';

create table CLMS_MEDICINE_PACKAGE_DETAIL (
   id int not null primary key auto_increment comment '主键',
   package_id int not null comment '包外键id',
   medicine_name varchar(200) comment '药品名称',
   medicine_category varchar(1) comment '药品分类',
   indication varchar(300) comment '适应症',
   valid_flag boolean comment '有效标志',
   remark varchar(100) comment '备注描述',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (package_id) references CLMS_MEDICINE_PACKAGE(id),
   index idx_package_id (package_id),
   index idx_medicine_name (medicine_name),
   index idx_medicine_category (medicine_category),
   index idx_valid_flag (valid_flag),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='药品模板药品表';

create table CLMS_MEDICAL_DIR (
   id int not null primary key auto_increment comment '目录id',
   dir_name varchar(100) comment '目录定义名称',
   parent_id int comment '上级目录id',
   dir_level tinyint comment '目录层级',
   valid_flag boolean comment '有效标志',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   index idx_dir_name (dir_name),
   index idx_parent_id (parent_id),
   index idx_dir_level (dir_level),
   index idx_valid_flag (valid_flag),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='医疗目录表';

create table CLMS_MEDICAL_ICD_DIR (
   id int not null primary key auto_increment comment '目录id',
   dianose_code varchar(20) not null comment '国际ICD编码',
   dir_id int comment '目录id',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (dir_id) references CLMS_MEDICAL_DIR(id),
   index idx_dianose_code (dianose_code),
   index idx_dir_id (dir_id),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='医疗ICD目录关系表';

create table CLMS_CLAUSE_CONFIG (
   id int not null primary key auto_increment comment '主键',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   pf_version varchar(10) comment '产品工厂的版本',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   version_no int not null comment '版本号',
   define_source char(1) default '1' comment '来源',
   duty_detail JSON comment '责任明细',
   ig_flag char(1) comment '个团标记',
   valid_flag varchar(1) comment '生效状态',
   effect_time datetime comment '生效时间',
   expire_time datetime comment '失效时间',
   add_reason varchar(1) comment '新增原因分类',
   add_reason_desc varchar(300) comment '新增原因描述',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_version_no (version_no),
   index idx_valid_flag (valid_flag),
   index idx_effect_time (effect_time),
   index idx_expire_time (expire_time),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款理赔配置表';

create table CLMS_CLAUSE_TASK (
   id int not null primary key auto_increment comment '主键',
   flow_in_id int comment '上一个任务id',
   config_id int not null comment '理赔配置id',
   version_no int not null comment '版本号',
   task_node varchar(20) not null comment '任务节点',
   flow_in_time datetime comment '流入时间',
   start_time datetime comment '开始处理时间',
   flow_out_time datetime comment '流出时间',
   node_status varchar(2) comment '节点状态',
   task_user_code varchar(30) comment '任务处理人',
   review_opinion varchar(100) comment '审核意见',
   remark varchar(200) comment '备注',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_version_no (version_no),
   index idx_task_node (task_node),
   index idx_node_status (node_status),
   index idx_task_user_code (task_user_code),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='流程任务表';

create table CLMS_CLAUSE_MATCH (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   match_sequece int not null comment '自动匹配顺序',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   bill_type varchar(3) comment '发票类型',
   match_keys varchar(200) comment '票据明细包含的关键字',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_match_sequece (match_sequece),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_duty_code (duty_code),
   index idx_match_keys (match_keys),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款匹责配置表';

create table CLMS_CLAUSE_MATCH_DISEASE (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   match_id int not null comment '自动匹责id',
   medical_dir1_id int not null comment '一级医疗目录id',
   medical_dir1_name varchar(200) comment '一级医疗目录名称',
   medical_dir2_id int not null comment '二级医疗目录id',
   medical_dir2_name varchar(200) not null comment '二级医疗目录名称',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   foreign key (match_id) references CLMS_CLAUSE_MATCH(id),
   foreign key (medical_dir1_id) references CLMS_MEDICAL_DIR(id),
   foreign key (medical_dir2_id) references CLMS_MEDICAL_DIR(id),
   index idx_config_id (config_id),
   index idx_match_id (match_id),
   index idx_medical_dir1_id (medical_dir1_id),
   index idx_medical_dir2_id (medical_dir2_id),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款匹责疾病范围表';

create table CLMS_INVOICE_MATCH (
   id int not null primary key auto_increment comment '主键',
   report_no varchar(20) not null comment '案件号',
   case_times int not null comment '赔付次数',
   match_type varchar(1) not null comment '匹责类型',
   config_id int not null comment '自动匹责的配置项id-自动匹责时填充',
   id_ahcs_invoice_info varchar(32) comment '发票账单信息表主键',
   match_sign tinyint comment '执行成功标志',
   fail_desc varchar(100) comment '失败描述',
   match_result json comment '匹责结论',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   index idx_report_no (report_no),
   index idx_case_times (case_times),
   index idx_match_type (match_type),
   index idx_config_id (config_id),
   index idx_id_ahcs_invoice_info (id_ahcs_invoice_info),
   index idx_match_sign (match_sign),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='发票匹责结果表';

create table CLMS_VERIFY_WAIT_PERIOD (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   disease_wait_period int comment '新保疾病等待期（天）',
   accident_wait_period int comment '新保意外等待期（天）',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-等待期表';

create table CLMS_VERIFY_HOSPITAL (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   verify_level tinyint not null comment '核责等级',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   hospital_level varchar(4) comment '医院等级',
   hospital_grade varchar(4) comment '医院级别',
   hospital_nature varchar(3) comment '医院性质',
   medical_consultation varchar(5) comment '就诊类型',
   hospital_region varchar(1) comment '医院区域类型',
   except_region json comment '除外省市',
   include_package_id int comment '协议扩展-医院包ID',
   exclude_package_id int comment '除外-医院包ID',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   foreign key (include_package_id) references CLMS_HOSPITAL_PACKAGE(id),
   foreign key (exclude_package_id) references CLMS_HOSPITAL_PACKAGE(id),
   index idx_config_id (config_id),
   index idx_verify_level (verify_level),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_duty_code (duty_code),
   index idx_hospital_level (hospital_level),
   index idx_hospital_grade (hospital_grade),
   index idx_hospital_nature (hospital_nature),
   index idx_medical_consultation (medical_consultation),
   index idx_hospital_region (hospital_region),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-医院范围表';

create table CLMS_VERIFY_SPECIFIC_HOSPITAL (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   v_hospital_id int not null comment '医院核责规则id',
   specific_type varchar(1) not null comment '特定类型',
   hospital_code varchar(20) comment '医院代码',
   hospital_name varchar(200) comment '医院名称',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   foreign key (v_hospital_id) references CLMS_VERIFY_HOSPITAL(id),
   index idx_config_id (config_id),
   index idx_v_hospital_id (v_hospital_id),
   index idx_specific_type (specific_type),
   index idx_hospital_code (hospital_code),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-特定医院表';

create table CLMS_VERIFY_DISEASE (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   verify_level tinyint not null comment '核责等级',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_verify_level (verify_level),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_duty_code (duty_code),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-疾病范围表';

create table CLMS_VERIFY_DISEASE_DIR (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   v_disease_id int not null comment '核责疾病范围id',
   disease_type varchar(1) not null comment '涵盖疾病类别',
   dir1_id int comment '一级疾病目录id',
   dir1_name varchar(200) comment '一级疾病目录名称',
   dir2_id varchar(20) comment '二级疾病目录id',
   dir2_name varchar(200) comment '二级疾病目录名称',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   foreign key (v_disease_id) references CLMS_VERIFY_DISEASE(id),
   foreign key (dir1_id) references CLMS_MEDICAL_DIR(id),
   foreign key (dir2_id) references CLMS_MEDICAL_DIR(id),
   index idx_config_id (config_id),
   index idx_v_disease_id (v_disease_id),
   index idx_disease_type (disease_type),
   index idx_dir1_id (dir1_id),
   index idx_dir2_id (dir2_id),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-疾病范围目录表';

create table CLMS_VERIFY_DISEASE_EXCLUDE (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   v_disease_id int not null comment '核责疾病范围id',
   vdd_id int not null comment '核责疾病目录id',
   dianose_code varchar(20) not null comment '国际ICD编码',
   disease_name varchar(200) comment '疾病名称',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   foreign key (v_disease_id) references CLMS_VERIFY_DISEASE(id),
   foreign key (vdd_id) references CLMS_VERIFY_DISEASE_DIR(id),
   index idx_config_id (config_id),
   index idx_v_disease_id (v_disease_id),
   index idx_vdd_id (vdd_id),
   index idx_dianose_code (dianose_code),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-疾病范围除外疾病表';

create table CLMS_VERIFY_CATALOG3 (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   verify_level tinyint not null comment '核责等级',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_verify_level (verify_level),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_duty_code (duty_code),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-三目录除外表';

create table CLMS_VERIFY_CATALOG3_PACK (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   catalog3_id int not null comment '核责三目录id',
   package_id int not null comment '药品包ID',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   foreign key (catalog3_id) references CLMS_VERIFY_CATALOG3(id),
   foreign key (package_id) references CLMS_MEDICINE_PACKAGE(id),
   index idx_config_id (config_id),
   index idx_catalog3_id (catalog3_id),
   index idx_package_id (package_id),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-三目录除外药品包表';

create table CLMS_VERIFY_CATALOG3_MEDICINE (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   catalog3_id int not null comment '核责三目录id',
   package_id int not null comment '药品包ID',
   medicine_id int not null comment '除外药所在包的id',
   medicine_name varchar(100) comment '药品名称',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   foreign key (catalog3_id) references CLMS_VERIFY_CATALOG3(id),
   foreign key (package_id) references CLMS_MEDICINE_PACKAGE(id),
   index idx_config_id (config_id),
   index idx_catalog3_id (catalog3_id),
   index idx_package_id (package_id),
   index idx_medicine_id (medicine_id),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-三目录除外除外药品表';

create table CLMS_VERIFY_OTHER_ITEM (
   id int not null primary key auto_increment comment '主键',
   verify_type varchar(2) not null comment '风险类型',
   verify_name varchar(100) not null comment '核责项名称',
   verify_level varchar(1) not null comment '核责等级',
   impl_class varchar(64) comment '实现类',
   process_flag varchar(1) comment '影响流程类型',
   remark varchar(200) comment '说明',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   index idx_verify_type (verify_type),
   index idx_verify_name (verify_name),
   index idx_verify_level (verify_level),
   index idx_impl_class (impl_class),
   index idx_process_flag (process_flag),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-其他核责项字典表';

create table CLMS_VERIFY_OTHER (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   verify_level tinyint not null comment '核责等级',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   verify_items varchar(300) comment '核责项目-需要枚举每一项对应的核验方法',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_verify_level (verify_level),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_duty_code (duty_code),
   index idx_verify_items (verify_items),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款核责配置-其他核责配置表';

create table CLMS_VERIFY_LOG (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   report_no varchar(20) not null comment '案件号',
   case_times int not null comment '赔付次数',
   verify_times int not null comment '核责次数',
   policy_no varchar(30) comment '匹配保单',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   match_times int not null comment '核责次数',
   verify_sign tinyint comment '执行成功标志',
   fail_desc varchar(100) comment '失败描述',
   valid_flag boolean comment '当前有效标志(最后一次记录有效，历史的记录无效)',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_report_no (report_no),
   index idx_case_times (case_times),
   index idx_verify_times (verify_times),
   index idx_policy_no (policy_no),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_match_times (match_times),
   index idx_verify_sign (verify_sign),
   index idx_valid_flag (valid_flag),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='自动核责日志主表';

create table CLMS_VERIFY_SUBLOG (
   id int not null primary key auto_increment comment '主键',
   verify_log_id int not null comment '日志id',
   verify_level varchar(1) not null comment '核责等级',
   verify_class varchar(1) not null comment '核责分类',
   id_ahcs_invoice_info varchar(32) comment '发票信息表主键',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   invoice_item_type varchar(1) comment '发票项目类别（当前仅针对药品分类）',
   invoice_item_code varchar(30) comment '发票项目代码（当前仅针对药品分类）',
   invoice_detail_name varchar(200) comment '发票明细名称',
   disclaim boolean comment '是否免责',
   disclaim_desc varchar(100) comment '免责描述',
   disclaim_value decimal(20,2) comment '免责金额',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (verify_log_id) references CLMS_VERIFY_LOG(id),
   index idx_verify_log_id (verify_log_id),
   index idx_verify_level (verify_level),
   index idx_verify_class (verify_class),
   index idx_id_ahcs_invoice_info (id_ahcs_invoice_info),
   index idx_duty_code (duty_code),
   index idx_duty_detail_code (duty_detail_code),
   index idx_invoice_item_type (invoice_item_type),
   index idx_invoice_item_code (invoice_item_code),
   index idx_disclaim (disclaim),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='自动核责日志子表';

create table CLMS_CALC_SEQ (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   calc_seq int comment '理算顺序',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   rel_duty_code varchar(60) comment '关联责任代码',
   rel_duty_name varchar(200) comment '关联责任名称',
   rel_duty_detail_code varchar(60) comment '关联责任明细代码',
   rel_duty_detail_name varchar(200) comment '关联责任明细名称',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_calc_seq (calc_seq),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_duty_code (duty_code),
   index idx_duty_detail_code (duty_detail_code),
   index idx_rel_duty_code (rel_duty_code),
   index idx_rel_duty_detail_code (rel_duty_detail_code),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款理算配置-理算顺序';

create table CLMS_DIFFER_AMOUNT (
   id int not null primary key auto_increment comment '主键',
   model_name varchar(200) comment '模板名称',
   differ_amount decimal(20,2) comment '差异化保额值',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   index idx_model_name (model_name),
   index idx_differ_amount (differ_amount),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款理算配置-差异化额度表';

create table CLMS_DIFFER_AMOUNT_FACTOR (
   id int not null primary key auto_increment comment '主键',
   da_id int not null comment '差异化额度表id',
   factor_code varchar(30) not null comment '因子代码',
   factor_name varchar(100) comment '因子名称',
   famular varchar(1) not null comment '匹配公式',
   factor_values varchar(300) comment '因子值',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (da_id) references CLMS_DIFFER_AMOUNT(id),
   index idx_da_id (da_id),
   index idx_factor_code (factor_code),
   index idx_factor_name (factor_name),
   index idx_famular (famular),
   index idx_factor_values (factor_values),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款理算配置-差异化额度因子表';

create table CLMS_FEE_CONFIG (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   config_level tinyint not null comment '约束等级',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   amount_limit decimal(20,2) comment '限额',
   amount_limit_differ_id int comment '差异化限额id-责任明细层级使用',
   amount_month_limit decimal(20,2) comment '月限额',
   amount_month_limit_type varchar(1) comment '月限额分类',
   amount_month_limit_differ_id int comment '差异化月限额',
   amount_day_limit decimal(20,2) comment '日限额',
   amount_day_limit_differ_id int comment '差异化日限额-责任明细层级使用',
   amount_share_flag boolean comment '是否存在共享限额',
   times_limit int comment '限制赔付次数',
   times_define varchar(1) comment '赔付次数定义',
   times_month_limit int comment '月赔付次数',
   times_month_limit_type varchar(1) comment '月赔付次数分类',
   times_month_define varchar(1) comment '月赔付次数定义',
   times_day_limit int comment '日赔付次数',
   times_day_define varchar(1) comment '日赔付次数定义',
   times_share_flag boolean comment '是否存在共享限次',
   year_deductible decimal(20,2) comment '年免赔额',
   day_deductible decimal(20,2) comment '日免赔额',
   times_deductible decimal(20,2) comment '次免赔额',
   times_deductible_define varchar(1) comment '次免赔额次数定义',
   deductible_share_flag boolean comment '是否存在共享免赔',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   foreign key (amount_limit_differ_id) references CLMS_DIFFER_AMOUNT(id),
   foreign key (amount_month_limit_differ_id) references CLMS_DIFFER_AMOUNT(id),
   foreign key (amount_day_limit_differ_id) references CLMS_DIFFER_AMOUNT(id),
   index idx_config_id (config_id),
   index idx_config_level (config_level),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_duty_code (duty_code),
   index idx_duty_detail_code (duty_detail_code),
   index idx_amount_limit (amount_limit),
   index idx_amount_month_limit (amount_month_limit),
   index idx_amount_day_limit (amount_day_limit),
   index idx_times_limit (times_limit),
   index idx_year_deductible (year_deductible),
   index idx_day_deductible (day_deductible),
   index idx_times_deductible (times_deductible),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款理算配置-费用约束表';

create table CLMS_LIMIT_SHARE (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   fee_config_id int not null comment '约束表id',
   share_type varchar(2) not null comment '共享类型',
   times_month_define varchar(1) comment '月赔付次数定义-用于月赔付次数',
   share_value decimal(20,2) comment '共享额度/次数',
   times_define varchar(1) comment '次定义',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   foreign key (fee_config_id) references CLMS_FEE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_fee_config_id (fee_config_id),
   index idx_share_type (share_type),
   index idx_share_value (share_value),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款理算配置-费用共享约束表';

create table CLMS_LIMIT_SHARE_REL (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   fee_config_id int not null comment '约束表id',
   fee_share_id int not null comment '共享约束表id',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   foreign key (fee_config_id) references CLMS_FEE_CONFIG(id),
   foreign key (fee_share_id) references CLMS_FEE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_fee_config_id (fee_config_id),
   index idx_fee_share_id (fee_share_id),
   index idx_duty_code (duty_code),
   index idx_duty_detail_code (duty_detail_code),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款理算配置-费用共享约束被关联方表';

create table CLMS_FEE_SCEN (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   config_level tinyint not null comment '约束等级',
   scen_type varchar(1) comment '场景类型',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   insurance_type_code VARCHAR(3) comment '医保类型-',
   visit_type varchar(16) comment '就诊类型',
   cost_codes varchar(10) comment '费用项目代码（特殊场景）',
   detail_value JSON comment '场景具体信息',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_config_level (config_level),
   index idx_scen_type (scen_type),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_duty_code (duty_code),
   index idx_duty_detail_code (duty_detail_code),
   index idx_insurance_type_code (insurance_type_code),
   index idx_visit_type (visit_type),
   index idx_cost_codes (cost_codes),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款理算配置-理算费用类场景配置表';

create table CLMS_ALLOWANCE_CONFIG (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   visit_type varchar(16) default 'HOSPITAL' comment '就诊类型',
   convert_hos_days boolean comment '是否这算有效住院天数',
   before_days int comment '前N天',
   after_days int comment '后N天',
   acc_pay_days int comment '累计赔付天数',
   once_pay_days int comment '单次赔付天数',
   once_nopay_days int comment '次免赔天数',
   allowance_per_day decimal(16,2) comment '津贴日额',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_duty_code (duty_code),
   index idx_visit_type (visit_type),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款理算配置-津贴类约束表';

create table CLMS_ALLOWANCE_SCEN (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   insurance_type_code VARCHAR(3) comment '医保类型',
   visit_type varchar(16) comment '就诊类型',
   detail_value JSON comment '场景具体信息',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_duty_code (duty_code),
   index idx_duty_detail_code (duty_detail_code),
   index idx_insurance_type_code (insurance_type_code),
   index idx_visit_type (visit_type),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='条款理算配置-理算津贴类场景配置表';

create table CLMS_CACULATE_LOG (
   id int not null primary key auto_increment comment '主键',
   config_id int not null comment '外键',
   report_no varchar(20) not null comment '案件号',
   case_times int not null comment '赔付次数',
   policy_no varchar(30) comment '匹配保单',
   product_code VARCHAR(32) not null comment '产品代码',
   product_name VARCHAR(100) comment '产品名称',
   plan_code VARCHAR(32) comment '险种代码',
   plan_name VARCHAR(100) comment '险种名称',
   risk_group_code VARCHAR(32) not null comment '方案代码',
   risk_group_name VARCHAR(100) comment '方案名称',
   calc_times int not null comment '理算次数',
   sum_pay decimal(20,2) comment '计算赔款金额（扣除非则部分）',
   disclaim_sum_pay decimal(20,2) comment '计算免责金额',
   valid_flag boolean comment '当前有效标志(最后一次记录有效，历史的记录无效)',
   calc_according text comment '理算依据',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (config_id) references CLMS_CLAUSE_CONFIG(id),
   index idx_config_id (config_id),
   index idx_report_no (report_no),
   index idx_case_times (case_times),
   index idx_policy_no (policy_no),
   index idx_product_code (product_code),
   index idx_plan_code (plan_code),
   index idx_risk_group_code (risk_group_code),
   index idx_calc_times (calc_times),
   index idx_sum_pay (sum_pay),
   index idx_disclaim_sum_pay (disclaim_sum_pay),
   index idx_valid_flag (valid_flag),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='自动理算日志主表';

create table CLMS_CACULATE_SUBLOG (
   id int not null primary key auto_increment comment '主键',
   caculate_log_id int not null comment '日志id',
   file_id varchar(256) comment '发票文件id',
   id_ahcs_invoice_info varchar(32) comment '发票信息表主键',
   scen_type varchar(1) comment '场景类型',
   scen_id int comment '使用自动理算场景配置id',
   PAY_PROPORTION decimal(10,2) comment '赔付比例',
   duty_code varchar(60) comment '责任代码',
   duty_name varchar(200) comment '责任名称',
   duty_detail_code varchar(60) comment '责任明细代码',
   duty_detail_name varchar(200) comment '责任明细名称',
   invoice_item_type varchar(1) comment '发票项目类别',
   invoice_item_code varchar(30) comment '发票项目代码（当前仅针对药品分类）',
   invoice_item_name varchar(200) comment '发票项目名称（针对药品分类）',
   invoice_detail_code varchar(30) comment '发票明细代码',
   invoice_detail_name varchar(200) comment '发票明细名称',
   self_pay_amount decimal(20,2) comment '自费金额',
   partial_self_pay_amount decimal(20,2) comment '部分自费金额',
   unreasonable_amount decimal(20,2) comment '理算不合理费用',
   third_party_payment decimal(20,2) comment '第三方支付',
   reasonable_medical_expense decimal(20,2) comment '理算合理医疗费用',
   calculated_compensation_amount decimal(20,2) comment '理算赔付金额',
   deduction_amount decimal(20,2) comment '扣费金额',
   created_by varchar(50) comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) comment '最新修改人员',
   sys_utime datetime not null default current_timestamp comment '最新修改时间',
   foreign key (caculate_log_id) references CLMS_CACULATE_LOG(id),
   index idx_caculate_log_id (caculate_log_id),
   index idx_file_id (file_id),
   index idx_id_ahcs_invoice_info (id_ahcs_invoice_info),
   index idx_scen_type (scen_type),
   index idx_scen_id (scen_id),
   index idx_duty_code (duty_code),
   index idx_duty_detail_code (duty_detail_code),
   index idx_invoice_item_type (invoice_item_type),
   index idx_invoice_item_code (invoice_item_code),
   index idx_sys_ctime (sys_ctime),
   index idx_sys_utime (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='自动理算日志子表';